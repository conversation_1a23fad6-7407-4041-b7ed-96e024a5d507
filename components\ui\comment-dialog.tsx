"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
   Dialog,
   DialogContent,
   DialogDescription,
   DialogHeader,
   DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useAlbumComments, useCreateComment } from "@/lib/hooks/use-comments";
import { CommentWithReplies } from "@/lib/models";
import { MessageCircle, Reply, Send, User } from "lucide-react";
import { useState } from "react";

interface CommentDialogProps {
   open: boolean;
   onOpenChange: (open: boolean) => void;
   albumId: string;
   albumName: string;
}

interface CommentFormData {
   content: string;
   authorName: string;
   authorEmail: string;
}

interface CommentItemProps {
   comment: CommentWithReplies;
   albumId: string;
   onReply: (parentId: string, authorName: string) => void;
}

function CommentItem({ comment, albumId, onReply }: CommentItemProps) {
   const [showReplyForm, setShowReplyForm] = useState(false);
   const [replyContent, setReplyContent] = useState("");
   const [replyAuthorName, setReplyAuthorName] = useState("");
   const [replyAuthorEmail, setReplyAuthorEmail] = useState("");
   const [isSubmittingReply, setIsSubmittingReply] = useState(false);

   const createComment = useCreateComment();

   const handleReplySubmit = async (e: React.FormEvent) => {
      e.preventDefault();
      if (!replyContent.trim() || !replyAuthorName.trim()) return;

      setIsSubmittingReply(true);
      try {
         await createComment.mutateAsync({
            albumId,
            parentId: comment._id?.toString(),
            content: replyContent.trim(),
            authorName: replyAuthorName.trim(),
            authorEmail: replyAuthorEmail.trim() || undefined,
         });

         setReplyContent("");
         setReplyAuthorName("");
         setReplyAuthorEmail("");
         setShowReplyForm(false);
      } catch (error) {
         console.error("Failed to submit reply:", error);
      } finally {
         setIsSubmittingReply(false);
      }
   };

   return (
      <div className="space-y-3">
         <div className="flex items-start gap-3">
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10 text-primary">
               <User className="h-4 w-4" />
            </div>
            <div className="flex-1 space-y-2">
               <div className="flex items-center gap-2">
                  <span className="font-medium text-foreground">
                     {comment.authorName}
                  </span>
                  <span className="text-xs text-muted-foreground">
                     {new Date(comment.createdAt).toLocaleDateString()}
                  </span>
               </div>
               <p className="text-sm text-foreground/90 leading-relaxed">
                  {comment.content}
               </p>
               <Button
                  variant="ghost"
                  size="sm"
                  onClick={() =>
                     onReply(comment._id?.toString() || "", comment.authorName)
                  }
                  className="h-auto p-0 text-xs text-muted-foreground hover:text-foreground"
               >
                  <Reply className="h-3 w-3 mr-1" />
                  Reply
               </Button>
            </div>
         </div>

         {/* Replies */}
         {comment.replies.length > 0 && (
            <div className="ml-11 space-y-3 border-l border-border/30 pl-4">
               {comment.replies.map((reply) => (
                  <div
                     key={reply._id?.toString()}
                     className="flex items-start gap-3"
                  >
                     <div className="flex h-6 w-6 items-center justify-center rounded-full bg-muted text-muted-foreground">
                        <User className="h-3 w-3" />
                     </div>
                     <div className="flex-1 space-y-1">
                        <div className="flex items-center gap-2">
                           <span className="text-sm font-medium text-foreground">
                              {reply.authorName}
                           </span>
                           <span className="text-xs text-muted-foreground">
                              {new Date(reply.createdAt).toLocaleDateString()}
                           </span>
                        </div>
                        <p className="text-sm text-foreground/90 leading-relaxed">
                           {reply.content}
                        </p>
                     </div>
                  </div>
               ))}
            </div>
         )}

         {/* Reply Form */}
         {showReplyForm && (
            <form onSubmit={handleReplySubmit} className="ml-11 space-y-3">
               <div className="space-y-2">
                  <Input
                     placeholder="Your name"
                     value={replyAuthorName}
                     onChange={(e) => setReplyAuthorName(e.target.value)}
                     required
                     className="text-sm"
                  />
                  <Input
                     type="email"
                     placeholder="Your email (optional)"
                     value={replyAuthorEmail}
                     onChange={(e) => setReplyAuthorEmail(e.target.value)}
                     className="text-sm"
                  />
                  <Textarea
                     placeholder={`Reply to ${comment.authorName}...`}
                     value={replyContent}
                     onChange={(e) => setReplyContent(e.target.value)}
                     required
                     className="min-h-20 text-sm"
                  />
               </div>
               <div className="flex gap-2">
                  <Button
                     type="submit"
                     size="sm"
                     disabled={
                        isSubmittingReply ||
                        !replyContent.trim() ||
                        !replyAuthorName.trim()
                     }
                     className="flex items-center gap-1"
                  >
                     <Send className="h-3 w-3" />
                     {isSubmittingReply ? "Posting..." : "Post Reply"}
                  </Button>
                  <Button
                     type="button"
                     variant="ghost"
                     size="sm"
                     onClick={() => setShowReplyForm(false)}
                  >
                     Cancel
                  </Button>
               </div>
            </form>
         )}
      </div>
   );
}

export function CommentDialog({
   open,
   onOpenChange,
   albumId,
   albumName,
}: CommentDialogProps) {
   const [formData, setFormData] = useState<CommentFormData>({
      content: "",
      authorName: "",
      authorEmail: "",
   });
   const [isSubmitting, setIsSubmitting] = useState(false);
   const [replyingTo, setReplyingTo] = useState<{
      parentId: string;
      authorName: string;
   } | null>(null);

   const { data: commentsResponse, isLoading } = useAlbumComments(albumId);
   const createComment = useCreateComment();

   const comments = commentsResponse?.data as CommentWithReplies[] | undefined;

   const handleSubmit = async (e: React.FormEvent) => {
      e.preventDefault();
      if (!formData.content.trim() || !formData.authorName.trim()) return;

      setIsSubmitting(true);
      try {
         await createComment.mutateAsync({
            albumId,
            parentId: replyingTo?.parentId,
            content: formData.content.trim(),
            authorName: formData.authorName.trim(),
            authorEmail: formData.authorEmail.trim() || undefined,
         });

         setFormData({ content: "", authorName: "", authorEmail: "" });
         setReplyingTo(null);
      } catch (error) {
         console.error("Failed to submit comment:", error);
      } finally {
         setIsSubmitting(false);
      }
   };

   const handleReply = (parentId: string, authorName: string) => {
      setReplyingTo({ parentId, authorName });
      setFormData((prev) => ({ ...prev, content: "" }));
   };

   const handleCancelReply = () => {
      setReplyingTo(null);
      setFormData((prev) => ({ ...prev, content: "" }));
   };

   return (
      <Dialog open={open} onOpenChange={onOpenChange}>
         <DialogContent className="max-w-2xl max-h-[80vh] overflow-hidden flex flex-col">
            <DialogHeader>
               <DialogTitle className="flex items-center gap-2">
                  <MessageCircle className="h-5 w-5" />
                  Comments for {albumName}
               </DialogTitle>
               <DialogDescription>
                  Share your thoughts about this album
               </DialogDescription>
            </DialogHeader>

            <div className="flex-1 overflow-hidden flex flex-col space-y-4">
               {/* Comments List */}
               <div className="flex-1 overflow-y-auto space-y-4 pr-2">
                  {isLoading ? (
                     <div className="flex items-center justify-center py-8">
                        <div className="text-muted-foreground">
                           Loading comments...
                        </div>
                     </div>
                  ) : comments && comments.length > 0 ? (
                     comments.map((comment) => (
                        <CommentItem
                           key={comment._id?.toString()}
                           comment={comment}
                           albumId={albumId}
                           onReply={handleReply}
                        />
                     ))
                  ) : (
                     <div className="flex items-center justify-center py-8">
                        <div className="text-center text-muted-foreground">
                           <MessageCircle className="h-8 w-8 mx-auto mb-2 opacity-50" />
                           <p>No comments yet</p>
                           <p className="text-sm">
                              Be the first to share your thoughts!
                           </p>
                        </div>
                     </div>
                  )}
               </div>

               {/* Comment Form */}
               <div className="border-t border-border/50 pt-4">
                  {replyingTo && (
                     <div className="mb-3 p-2 bg-muted/50 rounded-md text-sm">
                        <span className="text-muted-foreground">
                           Replying to{" "}
                        </span>
                        <span className="font-medium">
                           {replyingTo.authorName}
                        </span>
                        <Button
                           variant="ghost"
                           size="sm"
                           onClick={handleCancelReply}
                           className="ml-2 h-auto p-0 text-xs"
                        >
                           Cancel
                        </Button>
                     </div>
                  )}

                  <form onSubmit={handleSubmit} className="space-y-3">
                     <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        <Input
                           placeholder="Your name"
                           value={formData.authorName}
                           onChange={(e) =>
                              setFormData((prev) => ({
                                 ...prev,
                                 authorName: e.target.value,
                              }))
                           }
                           required
                        />
                        <Input
                           type="email"
                           placeholder="Your email (optional)"
                           value={formData.authorEmail}
                           onChange={(e) =>
                              setFormData((prev) => ({
                                 ...prev,
                                 authorEmail: e.target.value,
                              }))
                           }
                        />
                     </div>
                     <Textarea
                        placeholder={
                           replyingTo
                              ? `Reply to ${replyingTo.authorName}...`
                              : "Share your thoughts about this album..."
                        }
                        value={formData.content}
                        onChange={(e) =>
                           setFormData((prev) => ({
                              ...prev,
                              content: e.target.value,
                           }))
                        }
                        required
                        className="min-h-24"
                     />
                     <div className="flex justify-end">
                        <Button
                           type="submit"
                           disabled={
                              isSubmitting ||
                              !formData.content.trim() ||
                              !formData.authorName.trim()
                           }
                           className="flex items-center gap-2"
                        >
                           <Send className="h-4 w-4" />
                           {isSubmitting
                              ? "Posting..."
                              : replyingTo
                              ? "Post Reply"
                              : "Post Comment"}
                        </Button>
                     </div>
                  </form>
               </div>
            </div>
         </DialogContent>
      </Dialog>
   );
}
