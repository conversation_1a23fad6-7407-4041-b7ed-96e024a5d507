"use client";

import TextReveal from "@/components/animations/text-reveal";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { CommentDialog } from "@/components/ui/comment-dialog";
import { useAlbumCommentCount } from "@/lib/hooks/use-comments";
import { Album } from "@/lib/models/album";
import {
   ChatBubbleBottomCenterIcon,
   CloudArrowDownIcon,
   LockClosedIcon,
} from "@heroicons/react/24/solid";
import { motion, useScroll, useTransform } from "framer-motion";
import { ArrowLeft, Share2 } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useRef, useState } from "react";

export default function AlbumHero({
   album,
   // handleLike,
   handleShare,
   images = [],
   onDownloadAll,
}: // isLiked,
{
   album: Album;
   handleLike?: () => void;
   handleShare: () => void;
   isLiked?: boolean;
   images?: Array<{ url: string; filename?: string }>;
   onDownloadAll?: () => Promise<void> | void;
}) {
   const container = useRef(null);
   const [commentDialogOpen, setCommentDialogOpen] = useState(false);

   // Get comment count for the album
   const { data: commentCountResponse } = useAlbumCommentCount(
      album._id as string
   );
   const commentCount =
      commentCountResponse?.success && commentCountResponse.data
         ? (commentCountResponse.data as { count: number }).count
         : 0;
   const { scrollYProgress } = useScroll({
      target: container,
      offset: ["start start", "end start"],
   });

   const y = useTransform(scrollYProgress, [0, 1], ["0vh", "70vh"]);

   // Progressive darkening effect - starts at 0.4 opacity and goes to 0.8 as you scroll
   const overlayOpacity = useTransform(scrollYProgress, [0, 1], [0.4, 0.8]);

   // Additional darkening layer that kicks in more aggressively
   const darkOverlayOpacity = useTransform(scrollYProgress, [0.3, 1], [0, 0.9]);

   // Handle download all images as zip (fallback if parent doesn't inject)
   const handleDownloadAll = async () => {
      if (!images || images.length === 0) {
         alert("No images available to download");
         return;
      }

      try {
         // Dynamically import JSZip to avoid SSR issues
         const JSZip = (await import("jszip")).default;
         const zip = new JSZip();

         // Create a folder for the album
         const albumFolder = zip.folder(album.name.replace(/[^a-z0-9]/gi, "_"));

         // Download and add each image to the zip
         const downloadPromises = images.map(async (image, index) => {
            try {
               const response = await fetch(image.url);
               const blob = await response.blob();

               // Generate filename if not provided
               const filename = image.filename || `image_${index + 1}.jpg`;

               // Add to zip
               albumFolder?.file(filename, blob);
            } catch (error) {
               console.error(`Failed to download image ${index + 1}:`, error);
            }
         });

         // Wait for all downloads to complete
         await Promise.all(downloadPromises);

         // Generate and download the zip file
         const zipBlob = await zip.generateAsync({ type: "blob" });
         const url = URL.createObjectURL(zipBlob);

         const link = document.createElement("a");
         link.href = url;
         link.download = `${album.name.replace(/[^a-z0-9]/gi, "_")}_images.zip`;
         document.body.appendChild(link);
         link.click();
         document.body.removeChild(link);

         // Clean up the URL
         URL.revokeObjectURL(url);
      } catch (error) {
         console.error("Failed to create zip file:", error);
         alert("Failed to download images. Please try again.");
      }
   };

   return (
      <div ref={container}>
         <motion.div
            initial={{ opacity: 0, scale: 0.8, borderRadius: "50%" }}
            animate={{ opacity: 1, scale: 1, borderRadius: "0%" }}
            transition={{ duration: 0.4, delay: 0.8 }}
            className="h-screen overflow-hidden"
         >
            <motion.div style={{ y }} className="relative h-full">
               <Image
                  src={album.coverImageUrl || ""}
                  fill
                  alt="image"
                  className="object-cover"
               />

               {/* Base gradient overlay for text readability */}
               <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-black/20" />
               <div className="absolute inset-0 bg-gradient-to-r from-black/30 via-transparent to-black/30" />

               {/* Progressive darkening overlay */}
               <motion.div
                  style={{ opacity: overlayOpacity }}
                  className="absolute inset-0 bg-black"
               />

               {/* Additional darkening layer for more dramatic effect */}
               <motion.div
                  style={{ opacity: darkOverlayOpacity }}
                  className="absolute inset-0 bg-gradient-to-t from-black via-black/80 to-black/40"
               />
            </motion.div>

            {/* Central Hero Content */}
            <div className="container mx-auto px-4 flex items-center h-screen absolute inset-0 overflow-hidden z-10">
               <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8 }}
                  className="text-center max-w-4xl mx-auto"
               >
                  {/* Album Title */}
                  <TextReveal delay={1.6}>
                     <h1 className="text-4xl md:text-6xl lg:text-8xl font-bold text-white mb-4 leading-tight">
                        {album.name}
                     </h1>
                  </TextReveal>

                  {/* Album Description */}
                  {album.description && (
                     <TextReveal delay={2}>
                        <p className="sm:text-lg text-white/90 mb-8 leading-relaxed max-w-3xl mx-auto">
                           {album.description}
                        </p>
                     </TextReveal>
                  )}

                  {/* Action Buttons */}
                  <motion.div
                     initial={{ opacity: 0, y: 20 }}
                     animate={{ opacity: 1, y: 0 }}
                     transition={{ duration: 0.8, delay: 2.4 }}
                     className="flex flex-wrap justify-center gap-4"
                  >
                     {/* <Button onClick={handleLike} size="lg">
                        <Heart
                           className={cn("w-5 h-5", isLiked && "fill-current")}
                        />
                        {isLiked ? "Liked" : "Like"}
                     </Button> */}
                     <Button
                        onClick={() =>
                           onDownloadAll ? onDownloadAll() : handleDownloadAll()
                        }
                        size="lg"
                        className="bg-primary hover:bg-primary/90"
                     >
                        <CloudArrowDownIcon className="size-5 mr-1" />
                        Download All
                     </Button>
                     <Button
                        onClick={handleShare}
                        variant="outline"
                        className="border-border/60"
                        size="lg"
                     >
                        <Share2 className="size-5 mr-1" />
                        Share
                     </Button>
                     <Button
                        asChild
                        size="lg"
                        variant="outline"
                        className="border-border/60"
                     >
                        <Link
                           href="/gallery/albums"
                           className="flex items-center gap-2"
                        >
                           <ArrowLeft className="size-5 mr-1" />
                           Back to Albums
                        </Link>
                     </Button>
                  </motion.div>

                  <div className="flex justify-center items-center gap-4 mb-8 mt-6">
                     <Button
                        onClick={() => setCommentDialogOpen(true)}
                        variant="outline"
                        className="border-border/60 relative"
                        size="lg"
                     >
                        <ChatBubbleBottomCenterIcon className="size-5 mr-1" />
                        Comments
                        {commentCount > 0 && (
                           <span className="absolute -top-2 -right-2 bg-primary text-primary-foreground text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium">
                              {commentCount > 99 ? "99+" : commentCount}
                           </span>
                        )}
                     </Button>
                     {/* Badges */}
                     {album.hasPassword && (
                        <motion.div
                           initial={{ opacity: 0, scale: 0.8 }}
                           animate={{ opacity: 1, scale: 1 }}
                           transition={{ duration: 0.6, delay: 2.4 }}
                           className="flex justify-center"
                        >
                           <div className="relative group">
                              <div className="bg-gradient-to-r from-amber-500/20 to-orange-500/20 backdrop-blur-md rounded-lg px-2 py-2 border border-amber-400/30 hover:border-amber-400/50 transition-all duration-300">
                                 <div className="flex items-center gap-2">
                                    <LockClosedIcon className="w-4 h-4 text-amber-300" />
                                 </div>
                              </div>
                              {/* Glow effect */}
                              <div className="absolute inset-0 bg-gradient-to-r from-amber-500/20 to-orange-500/20 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                           </div>
                        </motion.div>
                     )}
                  </div>
               </motion.div>
            </div>
         </motion.div>

         {/* Comment Dialog */}
         <CommentDialog
            open={commentDialogOpen}
            onOpenChange={setCommentDialogOpen}
            albumId={album._id as string}
            albumName={album.name}
         />
      </div>
   );
}
