"use server";

import {
   Comment,
   CommentWithReplies,
   CreateCommentInput,
   DEFAULT_PAGINATION,
   PaginatedResponse,
   PaginationOptions,
   UpdateCommentInput,
   createCommentMetadata,
   createPaginationMetadata,
   validateCommentInput,
} from "@/lib/models";
import { getCollection } from "@/lib/mongodb/db";
import { ObjectId } from "mongodb";

/**
 * Get comments for an album with nested replies
 */
export async function getCommentsByAlbum(
   albumId: string
   // options: PaginationOptions = {}
): Promise<CommentWithReplies[]> {
   try {
      const commentCollection = await getCollection<Comment>("comments");

      // Get all comments for the album (no pagination for now to build the tree)
      const comments = await commentCollection
         .find({ albumId, isApproved: true })
         .sort({ createdAt: 1 })
         .toArray();

      // Build the comment tree
      const commentMap = new Map<string, CommentWithReplies>();
      const rootComments: CommentWithReplies[] = [];

      // First pass: create all comment objects
      comments.forEach((comment) => {
         const commentWithReplies: CommentWithReplies = {
            ...comment,
            _id: comment._id?.toString(),
            replies: [],
            replyCount: 0,
         };
         commentMap.set(comment._id?.toString() || "", commentWithReplies);
      });

      // Second pass: build the tree structure
      comments.forEach((comment) => {
         const commentWithReplies = commentMap.get(
            comment._id?.toString() || ""
         );
         if (!commentWithReplies) return;

         if (comment.parentId) {
            // This is a reply
            const parent = commentMap.get(comment.parentId);
            if (parent) {
               parent.replies.push(commentWithReplies);
               parent.replyCount++;
            }
         } else {
            // This is a root comment
            rootComments.push(commentWithReplies);
         }
      });

      return rootComments;
   } catch (error) {
      console.error("Error fetching comments by album:", error);
      throw new Error("Failed to fetch comments");
   }
}

/**
 * Get comment count for an album
 */
export async function getCommentCountByAlbum(albumId: string): Promise<number> {
   try {
      const commentCollection = await getCollection<Comment>("comments");
      return await commentCollection.countDocuments({
         albumId,
         isApproved: true,
      });
   } catch (error) {
      console.error("Error fetching comment count:", error);
      throw new Error("Failed to fetch comment count");
   }
}

/**
 * Create a new comment
 */
export async function createComment(
   input: CreateCommentInput
): Promise<Comment> {
   try {
      // Validate input
      const errors = validateCommentInput(input);
      if (errors.length > 0) {
         throw new Error(`Validation failed: ${errors.join(", ")}`);
      }

      const collection = await getCollection<Comment>("comments");
      const commentData = createCommentMetadata(input);

      const result = await collection.insertOne(commentData);

      if (!result.insertedId) {
         throw new Error("Failed to create comment");
      }

      const createdComment = await collection.findOne({
         _id: result.insertedId,
      });

      if (!createdComment) {
         throw new Error("Failed to retrieve created comment");
      }

      // Convert ObjectId to string for client component serialization
      return {
         ...createdComment,
         _id: createdComment._id?.toString(),
      };
   } catch (error) {
      console.error("Error creating comment:", error);
      throw error;
   }
}

/**
 * Update a comment
 */
export async function updateComment(
   id: string,
   input: UpdateCommentInput
): Promise<Comment | null> {
   try {
      const collection = await getCollection<Comment>("comments");

      const updateData = {
         ...input,
         updatedAt: new Date(),
      };

      const result = await collection.findOneAndUpdate(
         { _id: new ObjectId(id) },
         { $set: updateData },
         { returnDocument: "after" }
      );

      if (!result) return null;

      // Convert ObjectId to string for client component serialization
      return {
         ...result,
         _id: result._id?.toString(),
      };
   } catch (error) {
      console.error("Error updating comment:", error);
      throw new Error("Failed to update comment");
   }
}

/**
 * Delete a comment
 */
export async function deleteComment(id: string): Promise<boolean> {
   try {
      const collection = await getCollection<Comment>("comments");

      // First, delete all replies to this comment
      await collection.deleteMany({ parentId: id });

      // Then delete the comment itself
      const result = await collection.deleteOne({ _id: new ObjectId(id) });

      return result.deletedCount > 0;
   } catch (error) {
      console.error("Error deleting comment:", error);
      throw new Error("Failed to delete comment");
   }
}

/**
 * Get comment by ID
 */
export async function getCommentById(id: string): Promise<Comment | null> {
   try {
      const collection = await getCollection<Comment>("comments");
      const comment = await collection.findOne({ _id: new ObjectId(id) });

      if (!comment) return null;

      // Convert ObjectId to string for client component serialization
      return {
         ...comment,
         _id: comment._id?.toString(),
      };
   } catch (error) {
      console.error("Error fetching comment by ID:", error);
      throw new Error("Failed to fetch comment");
   }
}

/**
 * Get all comments with pagination (for admin)
 */
export async function getAllComments(
   options: PaginationOptions = {}
): Promise<PaginatedResponse<Comment>> {
   try {
      const {
         page = DEFAULT_PAGINATION.page,
         limit = DEFAULT_PAGINATION.limit,
         sortBy = DEFAULT_PAGINATION.sortBy,
         sortOrder = DEFAULT_PAGINATION.sortOrder,
      } = options;

      const commentCollection = await getCollection<Comment>("comments");

      // Build sort query
      const sort: Record<string, 1 | -1> = {};
      sort[sortBy] = sortOrder === "asc" ? 1 : -1;

      // Calculate skip value
      const skip = (page - 1) * limit;

      const [comments, total] = await Promise.all([
         commentCollection
            .find({})
            .sort(sort)
            .skip(skip)
            .limit(limit)
            .toArray(),
         commentCollection.countDocuments(),
      ]);

      const pagination = createPaginationMetadata(page, limit, total);

      // Convert ObjectIds to strings for client component serialization
      const serializedComments = comments.map((comment) => ({
         ...comment,
         _id: comment._id?.toString(),
      }));

      return {
         data: serializedComments,
         pagination,
      };
   } catch (error) {
      console.error("Error fetching all comments:", error);
      throw new Error("Failed to fetch comments");
   }
}
