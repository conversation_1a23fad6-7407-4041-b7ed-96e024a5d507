"use client";

import {
   createCommentAction,
   deleteCommentAction,
   getAlbumCommentCount,
   getAlbumComments,
   getCommentAction,
   updateCommentAction,
} from "@/lib/actions/comment-actions";
import { UpdateCommentInput } from "@/lib/models";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { invalidationKeys, queryKeys } from "./query-keys";

/**
 * Hook to fetch comments for an album
 */
export function useAlbumComments(albumId: string) {
   return useQuery({
      queryKey: queryKeys.comments.byAlbum(albumId),
      queryFn: () => getAlbumComments(albumId),
      enabled: !!albumId,
      staleTime: 2 * 60 * 1000, // 2 minutes
   });
}

/**
 * Hook to fetch comment count for an album
 */
export function useAlbumCommentCount(albumId: string) {
   return useQuery({
      queryKey: queryKeys.comments.count(albumId),
      queryFn: () => getAlbumCommentCount(albumId),
      enabled: !!albumId,
      staleTime: 5 * 60 * 1000, // 5 minutes
   });
}

/**
 * Hook to fetch a single comment by ID
 */
export function useComment(id: string) {
   return useQuery({
      queryKey: queryKeys.comments.detail(id),
      queryFn: () => getCommentAction(id),
      enabled: !!id,
      staleTime: 10 * 60 * 1000, // 10 minutes
   });
}

/**
 * Hook to create a new comment
 */
export function useCreateComment() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: createCommentAction,
      onSuccess: (data, variables) => {
         if (data.success && data.data) {
            // Invalidate comments for the specific album
            queryClient.invalidateQueries({
               queryKey: queryKeys.comments.byAlbum(variables.albumId),
            });

            // Invalidate comment count for the album
            queryClient.invalidateQueries({
               queryKey: queryKeys.comments.count(variables.albumId),
            });

            toast.success("Comment added successfully");
         }
      },
      onError: (error) => {
         toast.error(
            error instanceof Error ? error.message : "Failed to add comment"
         );
      },
   });
}

/**
 * Hook to update a comment
 */
export function useUpdateComment() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: ({ id, input }: { id: string; input: UpdateCommentInput }) =>
         updateCommentAction(id, input),
      onSuccess: (data, variables) => {
         if (data.success && data.data) {
            // Update the specific comment in cache
            queryClient.setQueryData(
               queryKeys.comments.detail(variables.id),
               data
            );

            // Invalidate album comments to ensure consistency
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.commentsList(),
            });

            toast.success("Comment updated successfully");
         }
      },
      onError: (error) => {
         toast.error(
            error instanceof Error ? error.message : "Failed to update comment"
         );
      },
   });
}

/**
 * Hook to delete a comment
 */
export function useDeleteComment() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: deleteCommentAction,
      onSuccess: (data) => {
         if (data.success) {
            // Invalidate all comment queries
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.allComments(),
            });
            toast.success("Comment deleted successfully");
         } else {
            toast.error("Failed to delete comment");
         }
      },
      onError: (error) => {
         toast.error(
            error instanceof Error ? error.message : "Failed to delete comment"
         );
      },
   });
}
