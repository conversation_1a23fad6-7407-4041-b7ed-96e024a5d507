import { z } from "zod";

/**
 * Schema for creating a new comment
 */
export const createCommentSchema = z.object({
   albumId: z.string().min(1, "Album ID is required"),
   parentId: z.string().optional(),
   content: z
      .string()
      .min(1, "Comment content is required")
      .max(1000, "Comment must be less than 1000 characters")
      .trim(),
   authorName: z
      .string()
      .min(1, "Name is required")
      .max(100, "Name must be less than 100 characters")
      .trim(),
   authorEmail: z
      .string()
      .email("Please enter a valid email address")
      .optional()
      .or(z.literal("")),
});

/**
 * Schema for updating a comment
 */
export const updateCommentSchema = z.object({
   content: z
      .string()
      .min(1, "Comment content is required")
      .max(1000, "Comment must be less than 1000 characters")
      .trim()
      .optional(),
   authorName: z
      .string()
      .min(1, "Name is required")
      .max(100, "Name must be less than 100 characters")
      .trim()
      .optional(),
   authorEmail: z
      .string()
      .email("Please enter a valid email address")
      .optional()
      .or(z.literal("")),
   isApproved: z.boolean().optional(),
});

/**
 * Type inference for create comment form
 */
export type CreateCommentFormData = z.infer<typeof createCommentSchema>;

/**
 * Type inference for update comment form
 */
export type UpdateCommentFormData = z.infer<typeof updateCommentSchema>;
