{"name": "astral-studio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@aws-sdk/client-s3": "^3.864.0", "@aws-sdk/s3-request-presigner": "^3.864.0", "@gsap/react": "^2.1.2", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.2.1", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.8", "@studio-freight/lenis": "^1.0.42", "@tanstack/react-query": "^5.85.3", "@tanstack/react-query-devtools": "^5.85.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "color": "^5.0.0", "date-fns": "^4.1.0", "framer-motion": "^12.23.12", "gsap": "^3.13.0", "jose": "^6.0.13", "jszip": "^3.10.1", "lucide-react": "^0.541.0", "mongodb": "^6.18.0", "motion": "^12.23.12", "next": "15.4.5", "next-themes": "^0.4.6", "radix-ui": "^1.4.3", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "zod": "^4.0.17"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/color": "^4.2.0", "@types/jszip": "^3.4.1", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.5", "tailwindcss": "^4", "tw-animate-css": "^1.3.6", "typescript": "^5"}}